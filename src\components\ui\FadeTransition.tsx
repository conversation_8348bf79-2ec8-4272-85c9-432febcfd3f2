'use client'

import React, { useState, useEffect } from 'react'

interface FadeTransitionProps {
  children: React.ReactNode
  show: boolean
  duration?: number
  className?: string
}

export function FadeTransition({ 
  children, 
  show, 
  duration = 300,
  className = '' 
}: FadeTransitionProps) {
  const [shouldRender, setShouldRender] = useState(show)
  const [isVisible, setIsVisible] = useState(show)

  useEffect(() => {
    if (show) {
      setShouldRender(true)
      // Small delay to ensure the element is rendered before starting transition
      const timer = setTimeout(() => setIsVisible(true), 10)
      return () => clearTimeout(timer)
    } else {
      setIsVisible(false)
      // Wait for transition to complete before removing from DOM
      const timer = setTimeout(() => setShouldRender(false), duration)
      return () => clearTimeout(timer)
    }
  }, [show, duration])

  if (!shouldRender) return null

  return (
    <div
      className={`transition-opacity ease-in-out ${className}`}
      style={{
        transitionDuration: `${duration}ms`,
        opacity: isVisible ? 1 : 0,
      }}
    >
      {children}
    </div>
  )
}

interface CrossFadeTransitionProps {
  children: React.ReactNode
  transitionKey: string | number
  duration?: number
  className?: string
}

export function CrossFadeTransition({
  children,
  transitionKey,
  duration = 300,
  className = ''
}: CrossFadeTransitionProps) {
  const [currentKey, setCurrentKey] = useState(transitionKey)
  const [currentChildren, setCurrentChildren] = useState(children)
  const [isTransitioning, setIsTransitioning] = useState(false)

  useEffect(() => {
    if (transitionKey !== currentKey) {
      setIsTransitioning(true)
      
      // Start fade out
      const fadeOutTimer = setTimeout(() => {
        setCurrentKey(transitionKey)
        setCurrentChildren(children)
        
        // Start fade in
        const fadeInTimer = setTimeout(() => {
          setIsTransitioning(false)
        }, 50)
        
        return () => clearTimeout(fadeInTimer)
      }, duration / 2)
      
      return () => clearTimeout(fadeOutTimer)
    }
  }, [transitionKey, currentKey, children, duration])

  return (
    <div
      className={`transition-opacity ease-in-out ${className}`}
      style={{
        transitionDuration: `${duration}ms`,
        opacity: isTransitioning ? 0 : 1,
      }}
    >
      {currentChildren}
    </div>
  )
}
