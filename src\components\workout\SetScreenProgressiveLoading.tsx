'use client'

import React from 'react'
import { SetProgressInfo } from './SetProgressInfo'
import { ExerciseInfo } from './ExerciseInfo'
import type { ExerciseModel, RecommendationModel } from '@/types'

interface SetScreenProgressiveLoadingProps {
  currentExercise?: ExerciseModel
  recommendation?: RecommendationModel | null
  isWarmup: boolean
  currentSetIndex: number
  totalSets: number
  showSetSaved: boolean
  performancePercentage: () => number | null
  isLoadingRecommendation?: boolean
}

export function SetScreenProgressiveLoading({
  currentExercise,
  recommendation,
  isWarmup,
  currentSetIndex,
  totalSets,
  showSetSaved,
  performancePercentage,
  isLoadingRecommendation = false,
}: SetScreenProgressiveLoadingProps) {
  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto pb-24">
        {/* Set Progress Info - Show immediately if available */}
        <SetProgressInfo
          isWarmup={isWarmup}
          currentSetIndex={currentSetIndex}
          totalSets={totalSets}
          showSetSaved={showSetSaved}
        />

        {/* Exercise Info - Show immediately with loading state for recommendation */}
        <div className="relative">
          <ExerciseInfo
            currentExercise={currentExercise}
            recommendation={recommendation}
            performancePercentage={performancePercentage()}
          />
          
          {/* Loading overlay for recommendation */}
          {isLoadingRecommendation && !recommendation && (
            <div className="absolute inset-0 bg-bg-secondary/80 backdrop-blur-sm flex items-center justify-center">
              <div className="flex items-center space-x-2 text-text-secondary">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-brand-primary border-t-transparent" />
                <span className="text-sm">Loading recommendations...</span>
              </div>
            </div>
          )}
        </div>

        {/* Set Inputs Area */}
        <div className="px-4 py-6">
          {recommendation ? (
            // Show placeholder for actual SetInputs - will be replaced when fully loaded
            <div className="space-y-6">
              <div className="text-center text-text-secondary">
                <div className="animate-spin rounded-full h-6 w-6 border-2 border-brand-primary border-t-transparent mx-auto mb-2" />
                <p className="text-sm">Preparing workout inputs...</p>
              </div>
            </div>
          ) : (
            // Show skeleton inputs while recommendation loads
            <div className="space-y-6">
              {/* Reps input skeleton */}
              <div className="space-y-2">
                <div className="h-5 w-12 bg-bg-tertiary rounded animate-pulse" />
                <div className="flex items-center space-x-3">
                  <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
                  <div className="flex-1 h-12 bg-bg-tertiary rounded-lg animate-pulse" />
                  <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
                </div>
              </div>

              {/* Weight input skeleton */}
              <div className="space-y-2">
                <div className="h-5 w-16 bg-bg-tertiary rounded animate-pulse" />
                <div className="flex items-center space-x-3">
                  <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
                  <div className="flex-1 h-12 bg-bg-tertiary rounded-lg animate-pulse" />
                  <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Fixed bottom action area */}
      <div className="fixed bottom-0 left-0 right-0 bg-bg-primary border-t border-brand-primary/10 p-4">
        <div className="max-w-lg mx-auto">
          <div className="h-12 w-full bg-brand-primary/20 rounded-lg animate-pulse flex items-center justify-center">
            <span className="text-text-secondary text-sm">Loading...</span>
          </div>
        </div>
      </div>
    </div>
  )
}
